import { Metadata } from 'next';
import ExpeditionForm from '@/components/admin/ExpeditionForm';

export const runtime = 'edge';



export const metadata: Metadata = {
  title: 'Edit Expedition - Mountain Bikers Admin',
  description: 'Edit expedition details and settings.',
};

interface EditExpeditionPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditExpeditionPage({ params }: EditExpeditionPageProps) {
  const { id } = await params;
  return <ExpeditionForm mode="edit" expeditionId={id} />;
}
