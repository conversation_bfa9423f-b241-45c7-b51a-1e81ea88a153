'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, getSession } from '@/lib/auth';

interface AuthContextType {
  session: Session | null;
  status: 'loading' | 'authenticated' | 'unauthenticated';
  update: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [status, setStatus] = useState<'loading' | 'authenticated' | 'unauthenticated'>('loading');

  const updateSession = async () => {
    try {
      setStatus('loading');
      const newSession = await getSession();
      setSession(newSession);
      setStatus(newSession.user ? 'authenticated' : 'unauthenticated');
    } catch (error) {
      console.error('Failed to update session:', error);
      setSession({ user: null });
      setStatus('unauthenticated');
    }
  };

  useEffect(() => {
    updateSession();
  }, []);

  return (
    <AuthContext.Provider value={{ session, status, update: updateSession }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useSession() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSession must be used within an AuthProvider');
  }
  return context;
}

// Compatibility with NextAuth's useSession hook
export function useAuth() {
  return useSession();
}
