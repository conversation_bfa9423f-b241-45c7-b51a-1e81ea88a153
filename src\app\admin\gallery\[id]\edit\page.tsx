"use client";

export const runtime = 'edge';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, X, Plus } from 'lucide-react';
import { toast } from 'sonner';

interface GalleryImage {
  id: string;
  title: string;
  description: string;
  url: string;
  thumbnail: string;
  alt: string;
  categories: string[];
  tags: string[];
  featured: boolean;
  isActive: boolean;
  sortOrder: number;
}

export default function EditGalleryImage() {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [image, setImage] = useState<GalleryImage | null>(null);
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const response = await fetch(`/api/admin/gallery/${params.id}`);
        const result = await response.json();

        if (result.success) {
          setImage(result.data);
        } else {
          toast.error("Failed to load image details");
          router.push('/admin/gallery');
        }
      } catch (error) {
        console.error('Error fetching image:', error);
        toast.error("Failed to load image details");
        router.push('/admin/gallery');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchImage();
    }
  }, [params.id, router]);

  const handleSave = async () => {
    if (!image) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/admin/gallery/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(image),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Image updated successfully");
        router.push('/admin/gallery');
      } else {
        toast.error(result.error || "Failed to update image");
      }
    } catch (error) {
      console.error('Error updating image:', error);
      toast.error("Failed to update image");
    } finally {
      setSaving(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && image && !image.tags.includes(newTag.trim())) {
      setImage({
        ...image,
        tags: [...image.tags, newTag.trim()]
      });
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    if (image) {
      setImage({
        ...image,
        tags: image.tags.filter(tag => tag !== tagToRemove)
      });
    }
  };

  const addCategory = (category: string) => {
    if (image && !image.categories.includes(category)) {
      setImage({
        ...image,
        categories: [...image.categories, category]
      });
    }
  };

  const removeCategory = (categoryToRemove: string) => {
    if (image) {
      setImage({
        ...image,
        categories: image.categories.filter(cat => cat !== categoryToRemove)
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!image) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Image Not Found</h2>
          <Link href="/admin/gallery">
            <Button>Back to Gallery</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/admin/gallery">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Gallery
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Image</h1>
              <p className="text-gray-600">Update image details and metadata</p>
            </div>
          </div>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Image Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="aspect-video relative rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={image.url}
                  alt={image.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="mt-4 text-sm text-gray-500">
                <p><strong>URL:</strong> {image.url}</p>
                <p><strong>Thumbnail:</strong> {image.thumbnail}</p>
              </div>
            </CardContent>
          </Card>

          {/* Edit Form */}
          <Card>
            <CardHeader>
              <CardTitle>Image Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={image.title}
                    onChange={(e) => setImage({ ...image, title: e.target.value })}
                    placeholder="Enter image title"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={image.description || ''}
                    onChange={(e) => setImage({ ...image, description: e.target.value })}
                    placeholder="Enter image description"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="alt">Alt Text</Label>
                  <Input
                    id="alt"
                    value={image.alt}
                    onChange={(e) => setImage({ ...image, alt: e.target.value })}
                    placeholder="Enter alt text for accessibility"
                  />
                </div>
              </div>

              {/* Categories */}
              <div>
                <Label>Categories</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {image.categories.map((category) => (
                    <Badge key={category} variant="secondary" className="flex items-center gap-1">
                      {category}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeCategory(category)}
                      />
                    </Badge>
                  ))}
                </div>
                <Select onValueChange={addCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Add category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="landscapes">Landscapes</SelectItem>
                    <SelectItem value="adventure">Adventure</SelectItem>
                    <SelectItem value="culture">Culture</SelectItem>
                    <SelectItem value="people">People</SelectItem>
                    <SelectItem value="bikes">Bikes</SelectItem>
                    <SelectItem value="wildlife">Wildlife</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Tags */}
              <div>
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {image.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add new tag"
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button onClick={addTag} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Settings */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="featured">Featured Image</Label>
                  <Switch
                    id="featured"
                    checked={image.featured}
                    onCheckedChange={(checked) => setImage({ ...image, featured: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="active">Active</Label>
                  <Switch
                    id="active"
                    checked={image.isActive}
                    onCheckedChange={(checked) => setImage({ ...image, isActive: checked })}
                  />
                </div>

                <div>
                  <Label htmlFor="sortOrder">Sort Order</Label>
                  <Input
                    id="sortOrder"
                    type="number"
                    value={image.sortOrder}
                    onChange={(e) => setImage({ ...image, sortOrder: parseInt(e.target.value) || 0 })}
                    placeholder="Enter sort order"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
