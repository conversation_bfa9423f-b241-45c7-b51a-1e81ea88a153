"use client";

import React from 'react';
import { signOut } from '@/lib/auth';
import { LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LogoutButtonProps {
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

export default function LogoutButton({ className, variant = 'outline' }: LogoutButtonProps) {
  const handleLogout = async () => {
    await signOut();
  };

  return (
    <Button
      onClick={handleLogout}
      variant={variant}
      className={className}
    >
      <LogOut className="h-4 w-4 mr-2" />
      Logout
    </Button>
  );
}
