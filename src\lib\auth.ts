// Custom authentication utilities for edge runtime compatibility

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

export interface Session {
  user: User | null;
}

// Client-side authentication functions
export async function signIn(email: string, password: string): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch('/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        action: 'signin'
      }),
    });

    const data = await response.json();

    if (data.success) {
      // Trigger a page reload to update the session
      window.location.reload();
      return { success: true };
    } else {
      return { success: false, error: data.error || 'Authentication failed' };
    }
  } catch (error) {
    console.error('Sign in error:', error);
    return { success: false, error: 'Network error' };
  }
}

export async function signOut(): Promise<void> {
  try {
    await fetch('/api/auth/signout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'signout'
      }),
    });

    // Redirect to login page
    window.location.href = '/admin/login';
  } catch (error) {
    console.error('Sign out error:', error);
    // Still redirect even if the request failed
    window.location.href = '/admin/login';
  }
}

export async function getSession(): Promise<Session> {
  try {
    const response = await fetch('/api/auth/session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'session'
      }),
    });

    const data = await response.json();
    return { user: data.user || null };
  } catch (error) {
    console.error('Get session error:', error);
    return { user: null };
  }
}

// Server-side session verification
export async function getServerSession(request: Request): Promise<Session> {
  try {
    const cookieHeader = request.headers.get('cookie');
    if (!cookieHeader) {
      return { user: null };
    }

    const sessionToken = cookieHeader
      .split(';')
      .find(c => c.trim().startsWith('session-token='))
      ?.split('=')[1];

    if (!sessionToken) {
      return { user: null };
    }

    // Verify token (same logic as in the API route)
    const parts = sessionToken.split('.');
    if (parts.length !== 3) return { user: null };

    const payload = JSON.parse(atob(parts[1]));
    const expectedSignature = btoa(`${parts[0]}.${parts[1]}.${process.env.NEXTAUTH_SECRET || 'fallback-secret'}`);

    if (parts[2] !== expectedSignature) return { user: null };
    if (payload.exp && Date.now() > payload.exp) return { user: null };

    return {
      user: {
        id: payload.id,
        name: payload.name,
        email: payload.email,
        role: payload.role
      }
    };
  } catch (error) {
    console.error('Server session error:', error);
    return { user: null };
  }
}
