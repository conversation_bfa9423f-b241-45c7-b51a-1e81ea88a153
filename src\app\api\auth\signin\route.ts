import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

// Simple JWT-like token creation (for edge runtime)
function createToken(payload: any): string {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const body = btoa(JSON.stringify(payload));
  const signature = btoa(`${header}.${body}.${process.env.NEXTAUTH_SECRET || 'fallback-secret'}`);
  return `${header}.${body}.${signature}`;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate credentials
    if (
      email === process.env.ADMIN_EMAIL &&
      password === process.env.ADMIN_PASSWORD
    ) {
      // Create session token
      const token = createToken({
        id: '1',
        name: process.env.ADMIN_NAME || 'Admin',
        email: email,
        role: 'admin',
        exp: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
      });

      const response = NextResponse.json({
        success: true,
        user: {
          id: '1',
          name: process.env.ADMIN_NAME || 'Admin',
          email: email,
          role: 'admin'
        }
      });

      // Set session cookie
      response.cookies.set('session-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60 // 7 days
      });

      return response;
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Auth error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
