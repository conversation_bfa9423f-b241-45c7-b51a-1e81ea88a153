/* Services Swiper Custom Styles */
.services-swiper {
  padding: 0 60px;
  overflow: visible;
}

.services-swiper .swiper-slide {
  height: auto;
  display: flex;
  flex-direction: column;
}

.services-swiper .swiper-slide > div {
  height: 100%;
}

/* Navigation buttons */
.services-swiper-button-prev,
.services-swiper-button-next {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.services-swiper:hover .services-swiper-button-prev,
.services-swiper:hover .services-swiper-button-next {
  opacity: 1;
  visibility: visible;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .services-swiper {
    padding: 0 20px;
  }
  
  .services-swiper-button-prev {
    left: -10px;
    transform: translateY(-50%) translateX(0);
  }
  
  .services-swiper-button-next {
    right: -10px;
    transform: translateY(-50%) translateX(0);
  }
}

@media (max-width: 640px) {
  .services-swiper {
    padding: 0 10px;
  }
  
  .services-swiper-button-prev,
  .services-swiper-button-next {
    width: 40px;
    height: 40px;
    opacity: 1;
    visibility: visible;
  }
  
  .services-swiper-button-prev {
    left: -5px;
  }
  
  .services-swiper-button-next {
    right: -5px;
  }
}
