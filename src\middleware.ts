import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export const runtime = 'edge';

function verifyToken(token: string): any {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    const payload = JSON.parse(atob(parts[1]));
    const expectedSignature = btoa(`${parts[0]}.${parts[1]}.${process.env.NEXTAUTH_SECRET || 'fallback-secret'}`);

    if (parts[2] !== expectedSignature) return null;
    if (payload.exp && Date.now() > payload.exp) return null;

    return payload;
  } catch {
    return null;
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow access to login page and auth API routes
  if (pathname === '/admin/login' || pathname.startsWith('/api/auth/')) {
    return NextResponse.next();
  }

  // Allow access to public API routes
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/admin/')) {
    return NextResponse.next();
  }

  // Check for session token for admin routes
  if (pathname.startsWith('/admin') || pathname.startsWith('/api/admin/')) {
    const sessionToken = request.cookies.get('session-token')?.value;

    if (!sessionToken) {
      // Redirect to login page with callback URL
      const loginUrl = new URL('/admin/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Verify the token
    const payload = verifyToken(sessionToken);
    if (!payload || payload.role !== 'admin') {
      // Invalid or expired token, redirect to login
      const loginUrl = new URL('/admin/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      const response = NextResponse.redirect(loginUrl);
      response.cookies.delete('session-token');
      return response;
    }

    // Valid admin session, allow access
    return NextResponse.next();
  }

  // For all other routes, allow access
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
