import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

function verifyToken(token: string): any {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(atob(parts[1]));
    const expectedSignature = btoa(`${parts[0]}.${parts[1]}.${process.env.NEXTAUTH_SECRET || 'fallback-secret'}`);
    
    if (parts[2] !== expectedSignature) return null;
    if (payload.exp && Date.now() > payload.exp) return null;
    
    return payload;
  } catch {
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get('session-token')?.value;
    if (!sessionToken) {
      return NextResponse.json({ user: null });
    }

    const payload = verifyToken(sessionToken);
    if (!payload) {
      const response = NextResponse.json({ user: null });
      response.cookies.delete('session-token');
      return response;
    }

    return NextResponse.json({
      user: {
        id: payload.id,
        name: payload.name,
        email: payload.email,
        role: payload.role
      }
    });
  } catch (error) {
    console.error('Session error:', error);
    return NextResponse.json(
      { user: null },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return POST(request);
}
