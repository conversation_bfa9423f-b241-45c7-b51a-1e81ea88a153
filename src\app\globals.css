@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-heading: var(--font-playfair);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

/* Custom styles for MountainBea */
.font-heading {
  font-family: var(--font-playfair);
}

.font-body {
  font-family: var(--font-inter);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-mountain {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Hero section background */
.hero-bg {
  position: relative;
  background: url('/IMG_0534.JPG') no-repeat center center;
  background-size: cover;
  min-height: 100vh;
  width: 100%;
}

.hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
  z-index: -1;
}

/* Responsive hero background */
@media (max-width: 768px) {
  .hero-bg {
    background-attachment: scroll;
    min-height: 80vh;
  }
}

/* Swiper customizations */
.swiper-pagination-bullet {
  @apply bg-white/50;
}

.swiper-pagination-bullet-active {
  @apply bg-white;
}

.swiper-button-next,
.swiper-button-prev {
  @apply text-white;
}

/* Animation utilities */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Gallery Swiper Styles */
.gallery-main-swiper {
  border-radius: 12px;
  overflow: hidden;
}

.gallery-main-swiper .swiper-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-main-swiper .swiper-pagination {
  bottom: 16px;
}

.gallery-main-swiper .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.7);
  opacity: 1;
  width: 8px;
  height: 8px;
}

.gallery-main-swiper .swiper-pagination-bullet-active {
  background: #3b82f6;
  transform: scale(1.2);
}

/* Hide default navigation */
.gallery-main-swiper .swiper-button-next,
.gallery-main-swiper .swiper-button-prev {
  display: none;
}

/* Thumbnail Swiper */
.gallery-thumbs-swiper {
  padding: 8px 0;
}

.gallery-thumbs-swiper .swiper-slide {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.gallery-thumbs-swiper .swiper-slide-thumb-active {
  opacity: 1;
}

.gallery-thumbs-swiper .swiper-slide:hover {
  opacity: 0.8;
}

/* Custom navigation hover effects */
.gallery-button-prev:hover,
.gallery-button-next:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Gallery navigation arrow sizes */
.gallery-button-prev svg,
.gallery-button-next svg {
  width: 20px;
  height: 20px;
}

/* Mobile responsive gallery */
@media (max-width: 768px) {
  .gallery-button-prev,
  .gallery-button-next {
    width: 36px;
    height: 36px;
  }

  .gallery-button-prev svg,
  .gallery-button-next svg {
    width: 16px;
    height: 16px;
  }

  .gallery-main-swiper .swiper-pagination-bullet {
    width: 6px;
    height: 6px;
  }
}
