"use client";

import { useSession } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export function useAdminAuth() {
  const { session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session?.user || session.user.role !== 'admin') {
      router.push('/admin/login');
    }
  }, [session, status, router]);

  return {
    session,
    status,
    isAdmin: session?.user?.role === 'admin',
    isLoading: status === 'loading',
    isAuthenticated: !!session?.user,
  };
}
