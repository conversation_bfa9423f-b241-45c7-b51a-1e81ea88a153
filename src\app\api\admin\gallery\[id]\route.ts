import { NextRequest, NextResponse } from 'next/server';
import { galleryService } from '@/lib/turso-service';

export const runtime = 'edge';

// GET /api/admin/gallery/[id] - Get single gallery image for admin
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const image = await galleryService.getById(id);

    if (!image) {
      return NextResponse.json(
        { success: false, error: 'Gallery image not found' },
        { status: 404 }
      );
    }

    // Transform data to match admin expected format
    const transformedImage = {
      ...image,
      _id: image.id,
      category: JSON.parse(image.categories || '[]')[0] || 'general',
      categories: JSON.parse(image.categories || '[]'),
      tags: JSON.parse(image.tags || '[]'),
      featured: <PERSON><PERSON><PERSON>(image.featured),
      isActive: <PERSON><PERSON><PERSON>(image.isActive)
    };

    return NextResponse.json({
      success: true,
      data: transformedImage
    });

  } catch (error) {
    console.error('Error fetching gallery image:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch gallery image' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/gallery/[id] - Update gallery image
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    console.log(`📝 Updating gallery image ${id}:`, data);

    const updatedImage = await galleryService.update(id, data);

    return NextResponse.json({
      success: true,
      message: 'Gallery image updated successfully!',
      data: {
        ...updatedImage,
        _id: id,
        id: id
      }
    });

  } catch (error) {
    console.error('Error updating gallery image:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update gallery image' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/gallery/[id] - Delete gallery image
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    console.log(`🗑️ Deleting gallery image ${id}`);

    // Delete from database
    const deletedImage = await galleryService.delete(id);

    // Note: File deletion is not supported in edge runtime
    // Files will need to be cleaned up manually or through a separate process
    if (deletedImage && deletedImage.url && deletedImage.url.startsWith('/images/gallery/')) {
      console.log(`🗑️ Image deleted from database: ${deletedImage.url}`);
      console.warn('Physical file cleanup not available in edge runtime');
    }

    return NextResponse.json({
      success: true,
      message: 'Gallery image deleted successfully!'
    });

  } catch (error) {
    console.error('Error deleting gallery image:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete gallery image' },
      { status: 500 }
    );
  }
}
