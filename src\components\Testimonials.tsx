"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Star, Quote, ExternalLink, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface Testimonial {
  name: string;
  location: string;
  rating: number;
  comment: string;
  image: string;
  expedition?: string;
  date?: string;
}

interface TestimonialsData {
  title: string;
  subtitle: string;
  testimonials: Testimonial[];
}

const Testimonials = () => {
  const [testimonialsData, setTestimonialsData] = useState<TestimonialsData>({
    title: 'What Our Travelers Say',
    subtitle: 'Real experiences from real adventurers',
    testimonials: []
  });
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  console.log('Testimonials component - loading state:', loading, 'mounted:', mounted);

  useEffect(() => {
    setMounted(true);
    console.log('useEffect triggered - starting fetch...');

    const fetchTestimonials = async () => {
      try {
        console.log('Fetching testimonials from API...');
        const response = await fetch('/api/admin/homepage');
        console.log('API response status:', response.status);

        if (response.ok) {
          const result = await response.json();
          console.log('API response result:', result);

          if (result.success && result.data && result.data.testimonials) {
            console.log('Setting testimonials data:', result.data.testimonials);
            setTestimonialsData(result.data.testimonials);
          } else {
            console.log('No testimonials found in API response');
          }
        } else {
          console.error('API response not ok:', response.status);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        console.log('Setting loading to false...');
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  // Generate avatar URLs based on reviewer name
  const getAvatarUrl = (testimonial: Testimonial, index: number) => {
    // Use custom image if provided, otherwise use default avatars
    if (testimonial.image && testimonial.image.trim()) {
      return testimonial.image;
    }

    const defaultAvatars = [
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    ];
    return defaultAvatars[index % defaultAvatars.length];
  };

  // Calculate average rating from testimonials
  const getAverageRating = () => {
    const testimonials = testimonialsData?.testimonials || [];
    if (testimonials.length === 0) return 4.8;
    const total = testimonials.reduce((sum, testimonial) => sum + testimonial.rating, 0);
    return Math.round((total / testimonials.length) * 10) / 10;
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };



  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge variant="outline" className="mb-4">
            Customer Reviews
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-heading font-bold text-gray-900 mb-6">
            {testimonialsData?.title || 'What Our Travelers Say'}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {testimonialsData?.subtitle || 'Real experiences from real adventurers'}
          </p>
        </motion.div>

        {/* Testimonials Carousel */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading testimonials...</p>
          </div>
        ) : (!testimonialsData?.testimonials || testimonialsData.testimonials.length === 0) ? (
          <div className="text-center py-12">
            <p className="text-gray-600">No testimonials available at the moment.</p>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Swiper
              modules={[Navigation, Pagination, Autoplay]}
              spaceBetween={30}
              slidesPerView={1}
              navigation
              pagination={{ clickable: true }}
              autoplay={{ delay: 5000, disableOnInteraction: false }}
              breakpoints={{
                640: { slidesPerView: 2 },
                1024: { slidesPerView: 3 },
              }}
              className="pb-12"
            >
              {(testimonialsData?.testimonials || []).map((testimonial, index) => (
                <SwiperSlide key={index}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card className="h-full bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                      <CardContent className="p-6">
                        {/* Quote Badge */}
                        <div className="flex justify-center mb-4">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <Quote className="h-6 w-6 text-blue-600" />
                          </div>
                        </div>

                        {/* Rating */}
                        <div className="flex justify-center mb-4">
                          {renderStars(testimonial.rating)}
                        </div>

                        {/* Review Text */}
                        <p className="text-gray-600 text-center mb-6 leading-relaxed italic">
                          "{testimonial.comment || testimonial.text || 'No comment available'}"
                        </p>

                        {/* Trip Info */}
                        <div className="text-center mb-4">
                          {testimonial.expedition && (
                            <Badge variant="secondary" className="mb-2">
                              {testimonial.expedition}
                            </Badge>
                          )}
                          {testimonial.date && (
                            <p className="text-sm text-gray-500 mb-2">
                              {new Date(testimonial.date).toLocaleDateString()}
                            </p>
                          )}
                          <div className="flex items-center justify-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            <span className="text-xs text-green-600">Verified Customer</span>
                          </div>
                        </div>

                        {/* User Info */}
                        <div className="flex items-center justify-center space-x-3">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={getAvatarUrl(testimonial, index)} alt={testimonial.name} />
                            <AvatarFallback>
                              {testimonial.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="text-center">
                            <p className="font-semibold text-gray-900">{testimonial.name}</p>
                            <p className="text-sm text-gray-500">{testimonial.location}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </SwiperSlide>
              ))}
            </Swiper>
          </motion.div>
        )}

        {/* Stats Section */}
        {(testimonialsData?.testimonials?.length || 0) > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-16 bg-white rounded-2xl p-8 shadow-lg"
          >
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">{getAverageRating()}/5</div>
                <p className="text-gray-600">Average Rating</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">{testimonialsData?.testimonials?.length || 0}+</div>
                <p className="text-gray-600">Happy Customers</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">100%</div>
                <p className="text-gray-600">Satisfaction Rate</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">2020</div>
                <p className="text-gray-600">Founded</p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default Testimonials;
